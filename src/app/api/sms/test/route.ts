import { NextRequest, NextResponse } from 'next/server';
import { SMSService } from '@/lib/sms';
import { PhoneValidator } from '@/lib/phone-validation';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { phoneNumber, message } = body;

    // Validate required fields
    if (!phoneNumber || !message) {
      return NextResponse.json(
        { success: false, error: 'Phone number and message are required' },
        { status: 400 }
      );
    }

    // Validate phone number format
    if (!PhoneValidator.isValid(phoneNumber)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid phone number format. Please use Kenyan format: +254 XXXXXXXXX'
        },
        { status: 400 }
      );
    }

    // Format phone number for SMS sending
    const formattedPhone = PhoneValidator.formatForStorage(phoneNumber);
    if (!formattedPhone) {
      return NextResponse.json(
        { success: false, error: 'Failed to format phone number' },
        { status: 400 }
      );
    }

    // Send test SMS with AFTKNG sender ID
    const result = await SMSService.sendSMS({
      to: formattedPhone,
      message: `Test SMS from Nova: ${message}`,
    });

    return NextResponse.json({
      success: true,
      message: 'SMS sent successfully',
      data: {
        phoneNumber: formattedPhone,
        senderId: 'AFTKNG',
        result: result
      }
    });

  } catch (error) {
    console.error('Error sending test SMS:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to send SMS',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to test SMS configuration
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'SMS service is configured',
      config: {
        senderId: 'AFTKNG',
        provider: 'Africa\'s Talking',
        supportedFormat: '+254 XXXXXXXXX',
        testEndpoint: '/api/sms/test'
      }
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'SMS service configuration error' },
      { status: 500 }
    );
  }
}
