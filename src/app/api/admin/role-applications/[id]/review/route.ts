import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import User from '@/models/User';
import { requireAdmin } from '@/lib/auth-middleware';
import { SMSService } from '@/lib/sms';
import mongoose from 'mongoose';

// RoleApplication model (same as before)
const RoleApplicationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  currentRole: {
    type: String,
    enum: ['student', 'mentor', 'employer', 'partner', 'admin'],
    required: true,
  },
  requestedRole: {
    type: String,
    enum: ['mentor', 'employer', 'partner'],
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'withdrawn'],
    default: 'pending',
  },
  applicationData: {
    reason: { type: String, required: true },
    experience: String,
    qualifications: [String],
    portfolio: String,
    references: [{
      name: String,
      email: String,
      relationship: String,
    }],
    companyInfo: {
      name: String,
      website: String,
      size: String,
      industry: String,
      verificationDocuments: [String],
    },
    partnershipInfo: {
      organizationName: String,
      organizationType: String,
      region: String,
      website: String,
      verificationDocuments: [String],
    },
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  reviewedAt: Date,
  reviewNotes: String,
  appliedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

const RoleApplication = mongoose.models.RoleApplication ||
  mongoose.model('RoleApplication', RoleApplicationSchema);

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requireAdmin(request, async (req) => {
    try {
      await connectDB();

      const body = await request.json();
      const { action, reviewNotes } = body;

      // Validate action
      if (!['approve', 'reject'].includes(action)) {
        return NextResponse.json(
          { success: false, error: 'Invalid action. Must be "approve" or "reject"' },
          { status: 400 }
        );
      }

      // Get the application
      const application = await RoleApplication.findById(params.id)
        .populate('userId', 'name email phone role');

      if (!application) {
        return NextResponse.json(
          { success: false, error: 'Role application not found' },
          { status: 404 }
        );
      }

      if (application.status !== 'pending') {
        return NextResponse.json(
          { success: false, error: 'Application has already been reviewed' },
          { status: 400 }
        );
      }

      const user = application.userId as typeof User.prototype;
      const newStatus = action === 'approve' ? 'approved' : 'rejected';

      // Update application
      application.status = newStatus;
      application.reviewedBy = req.user?.id;
      application.reviewedAt = new Date();
      application.reviewNotes = reviewNotes || '';

      await application.save();

      // If approved, update user role
      if (action === 'approve') {
        const previousRole = user.role;
        user.role = application.requestedRole;

        // Add to role history
        user.roleHistory = user.roleHistory || [];
        user.roleHistory.push({
          previousRole,
          newRole: application.requestedRole,
          changedBy: req.user?.id,
          changedAt: new Date(),
          reason: `Role application approved: ${application.applicationData.reason}`,
        });

        // Role-specific setup
        switch (application.requestedRole) {
          case 'mentor':
            user.mentorship = {
              isMentor: true,
              isApproved: true,
              approvedAt: new Date(),
              approvedBy: req.user?.id,
            };
            break;

          case 'employer':
            user.employment = {
              isEmployer: true,
              companyVerified: true,
              verifiedAt: new Date(),
              verifiedBy: req.user?.id,
              companyInfo: application.applicationData.companyInfo,
            };
            break;

          case 'partner':
            user.partnership = {
              isPartner: true,
              isApproved: true,
              approvedAt: new Date(),
              approvedBy: req.user?.id,
              organizationInfo: application.applicationData.partnershipInfo,
            };
            break;
        }

        await user.save();
      }

      // Send notification to user
      try {
        if (user.phone) {
          const smsType = action === 'approve' ? 'role_approved' : 'role_rejected';
          await SMSService.sendSMS(
            user.phone,
            smsType,
            {
              userName: user.name,
              requestedRole: application.requestedRole,
              reviewNotes: reviewNotes || '',
              adminName: req.user?.name || 'Administrator',
            }
          );
        }
      } catch (smsError) {
        console.error('Error sending role review SMS:', smsError);
      }

      // Log the action
      console.log(`Role application ${action}d: ${user.email} -> ${application.requestedRole} by ${req.user?.email}`);

      return NextResponse.json({
        success: true,
        data: application,
        message: `Role application ${action}d successfully`,
      });
    } catch (error) {
      console.error('Error reviewing role application:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to review role application' },
        { status: 500 }
      );
    }
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return requireAdmin(request, async () => {
    try {
      await connectDB();

      const application = await RoleApplication.findById(params.id)
        .populate('userId', 'name email profile avatar createdAt')
        .populate('reviewedBy', 'name email');

      if (!application) {
        return NextResponse.json(
          { success: false, error: 'Role application not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: application,
      });
    } catch (error) {
      console.error('Error fetching role application:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch role application' },
        { status: 500 }
      );
    }
  });
}
