import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { Event } from '@/models/Event';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const status = searchParams.get('status') || 'published';
    const featured = searchParams.get('featured') === 'true';
    const upcoming = searchParams.get('upcoming') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const skip = (page - 1) * limit;

    // Build query
    const query: Record<string, unknown> = { status };

    if (type) {
      query.type = type;
    }

    if (category) {
      query.category = category;
    }

    if (featured) {
      query.featured = true;
    }

    if (upcoming) {
      query['schedule.startDate'] = { $gte: new Date() };
    }

    const events = await Event.find(query)
      .populate('organizer.userId', 'name email')
      .sort({
        featured: -1,
        'schedule.startDate': upcoming ? 1 : -1
      })
      .skip(skip)
      .limit(limit);

    const total = await Event.countDocuments(query);

    // Get filter options
    const eventTypes = await Event.distinct('type', { status: 'published' });
    const categories = await Event.distinct('category', { status: 'published' });

    return NextResponse.json({
      success: true,
      data: {
        events,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        filters: {
          types: eventTypes,
          categories,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const body = await request.json();
    const {
      title,
      description,
      type,
      category,
      organizer,
      speakers,
      schedule,
      location,
      registration,
      content,
      tags,
      thumbnail,
      visibility = 'public',
    } = body;

    // Validate required fields
    if (!title || !description || !type || !category || !schedule) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate dates
    const startDate = new Date(schedule.startDate);
    const endDate = new Date(schedule.endDate);

    if (startDate >= endDate) {
      return NextResponse.json(
        { success: false, error: 'End date must be after start date' },
        { status: 400 }
      );
    }

    if (startDate <= new Date()) {
      return NextResponse.json(
        { success: false, error: 'Start date must be in the future' },
        { status: 400 }
      );
    }

    // Create event
    const event = new Event({
      title,
      description,
      type,
      category,
      organizer: {
        ...organizer,
        userId: session.user.id,
      },
      speakers: speakers || [],
      schedule: {
        ...schedule,
        startDate,
        endDate,
      },
      location,
      registration: {
        isRequired: registration?.isRequired ?? true,
        maxAttendees: registration?.maxAttendees,
        deadline: registration?.deadline ? new Date(registration.deadline) : undefined,
        fee: registration?.fee || 0,
        currency: registration?.currency || 'USD',
        isFree: registration?.fee ? false : true,
        requiresApproval: registration?.requiresApproval || false,
        currentAttendees: 0,
      },
      content,
      tags: tags || [],
      thumbnail,
      visibility,
      status: 'draft', // Events start as draft
    });

    await event.save();

    return NextResponse.json({
      success: true,
      data: event,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create event' },
      { status: 500 }
    );
  }
}
