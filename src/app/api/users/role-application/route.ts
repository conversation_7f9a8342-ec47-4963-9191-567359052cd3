import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectDB } from '@/lib/mongodb';
import { User } from '@/models/User';
import { UserRole, PermissionChecker } from '@/lib/permissions';
import { SMSService } from '@/lib/sms';
import mongoose, { Document, Schema } from 'mongoose';

// Role Application Model
interface IRoleApplication extends Document {
  _id: string;
  userId: string;
  currentRole: UserRole;
  requestedRole: UserRole;
  status: 'pending' | 'approved' | 'rejected' | 'withdrawn';
  applicationData: {
    reason: string;
    experience?: string;
    qualifications?: string[];
    portfolio?: string;
    references?: {
      name: string;
      email: string;
      relationship: string;
    }[];
    companyInfo?: {
      name: string;
      website: string;
      size: string;
      industry: string;
      verificationDocuments?: string[];
    };
    partnershipInfo?: {
      organizationName: string;
      organizationType: string;
      region: string;
      website: string;
      verificationDocuments?: string[];
    };
  };
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  appliedAt: Date;
  updatedAt: Date;
}

const RoleApplicationSchema = new Schema<IRoleApplication>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  currentRole: {
    type: String,
    enum: ['student', 'mentor', 'organisation', 'admin'],
    required: true,
  },
  requestedRole: {
    type: String,
    enum: ['mentor', 'organisation'],
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'withdrawn'],
    default: 'pending',
  },
  applicationData: {
    reason: { type: String, required: true },
    experience: String,
    qualifications: [String],
    portfolio: String,
    references: [{
      name: String,
      email: String,
      relationship: String,
    }],
    companyInfo: {
      name: String,
      website: String,
      size: String,
      industry: String,
      verificationDocuments: [String],
    },
    partnershipInfo: {
      organizationName: String,
      organizationType: String,
      region: String,
      website: String,
      verificationDocuments: [String],
    },
  },
  reviewedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
  },
  reviewedAt: Date,
  reviewNotes: String,
  appliedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

// Compound index to prevent duplicate applications
RoleApplicationSchema.index({ userId: 1, requestedRole: 1, status: 1 });

const RoleApplication = mongoose.models.RoleApplication ||
  mongoose.model<IRoleApplication>('RoleApplication', RoleApplicationSchema);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { requestedRole, applicationData } = body;

    // Validate requested role
    const validTargetRoles: UserRole[] = ['mentor', 'employer', 'partner'];
    if (!validTargetRoles.includes(requestedRole)) {
      return NextResponse.json(
        { success: false, error: 'Invalid role requested' },
        { status: 400 }
      );
    }

    // Check if user can upgrade to this role
    if (!PermissionChecker.canUpgradeToRole(user.role as UserRole, requestedRole)) {
      return NextResponse.json(
        { success: false, error: 'Role upgrade not allowed from your current role' },
        { status: 400 }
      );
    }

    // Check for existing pending application
    const existingApplication = await RoleApplication.findOne({
      userId: session.user.id,
      requestedRole,
      status: 'pending',
    });

    if (existingApplication) {
      return NextResponse.json(
        { success: false, error: 'You already have a pending application for this role' },
        { status: 409 }
      );
    }

    // Validate application data based on requested role
    if (!applicationData.reason) {
      return NextResponse.json(
        { success: false, error: 'Reason for role request is required' },
        { status: 400 }
      );
    }

    if (requestedRole === 'employer' && !applicationData.companyInfo) {
      return NextResponse.json(
        { success: false, error: 'Company information is required for employer role' },
        { status: 400 }
      );
    }

    if (requestedRole === 'partner' && !applicationData.partnershipInfo) {
      return NextResponse.json(
        { success: false, error: 'Partnership information is required for partner role' },
        { status: 400 }
      );
    }

    // Create role application
    const roleApplication = new RoleApplication({
      userId: session.user.id,
      currentRole: user.role,
      requestedRole,
      applicationData,
    });

    await roleApplication.save();

    // Notify admins about new role application
    try {
      const admins = await User.find({ role: 'admin', isActive: true });
      for (const admin of admins) {
        if (admin.phone) {
          await SMSService.sendSMS(
            admin.phone,
            'role_application',
            {
              adminName: admin.name,
              applicantName: user.name,
              applicantEmail: user.email,
              requestedRole,
              currentRole: user.role,
            }
          );
        }
      }
    } catch (smsError) {
      console.error('Error sending role application SMS to admins:', smsError);
    }

    return NextResponse.json({
      success: true,
      data: roleApplication,
      message: 'Role application submitted successfully. You will be notified once reviewed.',
    }, { status: 201 });
  } catch (error) {
    console.error('Error submitting role application:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit role application' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectDB();

    const applications = await RoleApplication.find({
      userId: session.user.id,
    })
    .populate('reviewedBy', 'name email')
    .sort({ appliedAt: -1 });

    return NextResponse.json({
      success: true,
      data: applications,
    });
  } catch (error) {
    console.error('Error fetching role applications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch role applications' },
      { status: 500 }
    );
  }
}
