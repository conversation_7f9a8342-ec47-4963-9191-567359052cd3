'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import {
  Play,
  Clock,
  Users,
  Star,
  BookOpen,
  Award,
  CheckCircle,
  ArrowLeft
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Course {
  _id: string;
  title: string;
  description: string;
  instructor: string;
  instructorId: {
    name: string;
    avatar?: string;
    bio?: string;
  };
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  thumbnail: string;
  tags: string[];
  modules: {
    title: string;
    description: string;
    order: number;
    lessons: {
      title: string;
      type: 'video' | 'text' | 'quiz' | 'assignment';
      content: string;
      duration?: number;
      order: number;
    }[];
  }[];
  prerequisites: string[];
  learningOutcomes: string[];
  certification: {
    available: boolean;
    passingScore?: number;
  };
  pricing: {
    isFree: boolean;
    price?: number;
    currency: string;
  };
  enrollment: {
    totalStudents: number;
  };
  ratings: {
    average: number;
    totalRatings: number;
  };
  isPublished: boolean;
}

interface Enrollment {
  _id: string;
  progress: {
    completedModules: number;
    totalModules: number;
    completedLessons: string[];
    currentModule: number;
    currentLesson: number;
    timeSpent: number;
  };
  enrolledAt: string;
}

export default function CourseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();
  const [course, setCourse] = useState<Course | null>(null);
  const [enrollment, setEnrollment] = useState<Enrollment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [isEnrolled, setIsEnrolled] = useState(false);

  useEffect(() => {
    fetchCourseDetails();
  }, [params.id, fetchCourseDetails]);

  const fetchCourseDetails = useCallback(async () => {
    try {
      const response = await fetch(`/api/courses/${params.id}`);
      const result = await response.json();

      if (result.success) {
        setCourse(result.data.course);
        setEnrollment(result.data.enrollment);
        setIsEnrolled(result.data.isEnrolled);
      } else {
        toast.error('Course not found');
        router.push('/courses');
      }
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Failed to load course details');
    } finally {
      setIsLoading(false);
    }
  }, [params.id, router]);

  const handleEnroll = async () => {
    if (!session) {
      toast.error('Please sign in to enroll in courses');
      router.push('/auth/signin');
      return;
    }

    setIsEnrolling(true);
    try {
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ courseId: params.id }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Successfully enrolled in course!');
        setIsEnrolled(true);
        // Refresh course data to get enrollment info
        fetchCourseDetails();
      } else {
        toast.error(result.error || 'Failed to enroll in course');
      }
    } catch (error) {
      console.error('Error enrolling in course:', error);
      toast.error('Failed to enroll in course');
    } finally {
      setIsEnrolling(false);
    }
  };

  const getTotalLessons = () => {
    if (!course) return 0;
    return course.modules.reduce((total, module) => total + module.lessons.length, 0);
  };

  const getCompletionPercentage = () => {
    if (!enrollment || !course) return 0;
    const totalLessons = getTotalLessons();
    if (totalLessons === 0) return 0;
    return Math.round((enrollment.progress.completedLessons.length / totalLessons) * 100);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen galaxy-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Course Not Found</h1>
          <button
            onClick={() => router.push('/courses')}
            className="bg-galaxy-blue text-white px-6 py-2 rounded-lg hover:bg-galaxy-blue-light transition-colors"
          >
            Back to Courses
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen galaxy-background">
      <div className="star-field"></div>
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <div className="relative z-10 section-container py-8">
        {/* Back Button */}
        <button
          onClick={() => router.back()}
          className="flex items-center space-x-2 text-galaxy-blue hover:text-galaxy-blue-light transition-colors mb-6"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Back to Courses</span>
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Course Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <div className="flex items-start space-x-4">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-2xl glow-border">
                  <BookOpen className="h-10 w-10" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs font-medium galaxy-text-blue bg-blue-600/20 px-2 py-1 rounded-full">
                      {course.category}
                    </span>
                    <span className="text-xs text-gray-400 capitalize">
                      {course.level}
                    </span>
                  </div>
                  <h1 className="text-3xl font-bold text-white font-orbitron mb-2">
                    {course.title}
                  </h1>
                  <p className="text-gray-300 mb-4">
                    By {course.instructorId?.name || course.instructor}
                  </p>
                  <div className="flex items-center space-x-6 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{course.duration} hours</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{course.enrollment.totalStudents} students</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400" />
                      <span>{course.ratings.average.toFixed(1)} ({course.ratings.totalRatings} reviews)</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Progress Bar for Enrolled Students */}
              {isEnrolled && enrollment && (
                <div className="mt-6 p-4 bg-galaxy-blue/10 border border-galaxy-blue/20 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-galaxy-blue font-semibold">Your Progress</span>
                    <span className="text-white font-bold">{getCompletionPercentage()}%</span>
                  </div>
                  <div className="bg-galaxy-deep rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-galaxy-blue to-galaxy-blue-light h-3 rounded-full transition-all duration-300"
                      style={{ width: `${getCompletionPercentage()}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between mt-2 text-sm text-gray-300">
                    <span>{enrollment.progress.completedLessons.length} of {getTotalLessons()} lessons completed</span>
                    <span>{Math.round(enrollment.progress.timeSpent / 60)} hours spent</span>
                  </div>
                </div>
              )}
            </motion.div>

            {/* Course Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">About This Course</h2>
              <div className="text-gray-300 whitespace-pre-wrap">
                {course.description}
              </div>
            </motion.div>

            {/* Learning Outcomes */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">What You&apos;ll Learn</h2>
              <ul className="space-y-2">
                {course.learningOutcomes.map((outcome, index) => (
                  <li key={index} className="flex items-start space-x-2 text-gray-300">
                    <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                    <span>{outcome}</span>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Course Modules */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h2 className="text-xl font-semibold text-white mb-4">Course Content</h2>
              <div className="space-y-4">
                {course.modules.map((module, moduleIndex) => (
                  <div key={moduleIndex} className="border border-white/10 rounded-lg">
                    <div className="p-4 bg-white/5">
                      <h3 className="font-semibold text-white mb-2">
                        Module {module.order}: {module.title}
                      </h3>
                      <p className="text-gray-300 text-sm mb-2">{module.description}</p>
                      <span className="text-xs text-gray-400">
                        {module.lessons.length} lessons
                      </span>
                    </div>
                    <div className="divide-y divide-white/10">
                      {module.lessons.map((lesson, lessonIndex) => {
                        const isCompleted = enrollment?.progress.completedLessons.includes(`${moduleIndex}-${lessonIndex}`);
                        return (
                          <div key={lessonIndex} className="p-3 flex items-center justify-between hover:bg-white/5 transition-colors">
                            <div className="flex items-center space-x-3">
                              {isCompleted ? (
                                <CheckCircle className="h-4 w-4 text-green-400" />
                              ) : (
                                <div className="w-4 h-4 border border-gray-400 rounded-full" />
                              )}
                              <div>
                                <span className="text-white text-sm">{lesson.title}</span>
                                <div className="flex items-center space-x-2 text-xs text-gray-400">
                                  <span className="capitalize">{lesson.type}</span>
                                  {lesson.duration && (
                                    <>
                                      <span>•</span>
                                      <span>{lesson.duration} min</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            {isEnrolled && (
                              <button
                                onClick={() => router.push(`/courses/${course._id}/learn?module=${moduleIndex}&lesson=${lessonIndex}`)}
                                className="text-galaxy-blue hover:text-galaxy-blue-light transition-colors"
                              >
                                <Play className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Enrollment Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              {isEnrolled ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">Enrolled</h3>
                    <p className="text-gray-300 text-sm mb-4">
                      Continue your learning journey
                    </p>
                  </div>
                  <button
                    onClick={() => router.push(`/courses/${course._id}/learn`)}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 glow-border"
                  >
                    Continue Learning
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">
                      {course.pricing.isFree ? 'Free' : `${course.pricing.currency} ${course.pricing.price}`}
                    </div>
                    <p className="text-gray-300 text-sm">
                      Full lifetime access
                    </p>
                  </div>
                  <button
                    onClick={handleEnroll}
                    disabled={isEnrolling}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed glow-border"
                  >
                    {isEnrolling ? 'Enrolling...' : 'Enroll Now'}
                  </button>
                </div>
              )}
            </motion.div>

            {/* Course Info */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Course Details</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Duration</span>
                  <span className="text-white">{course.duration} hours</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Lessons</span>
                  <span className="text-white">{getTotalLessons()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Level</span>
                  <span className="text-white capitalize">{course.level}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Students</span>
                  <span className="text-white">{course.enrollment.totalStudents}</span>
                </div>
                {course.certification.available && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Certificate</span>
                    <Award className="h-4 w-4 text-yellow-400" />
                  </div>
                )}
              </div>
            </motion.div>

            {/* Prerequisites */}
            {course.prerequisites.length > 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="muted-glassmorphic rounded-xl p-6 glow-border"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Prerequisites</h3>
                <ul className="space-y-2">
                  {course.prerequisites.map((prereq, index) => (
                    <li key={index} className="flex items-start space-x-2 text-gray-300">
                      <CheckCircle className="h-4 w-4 text-galaxy-blue mt-1 flex-shrink-0" />
                      <span>{prereq}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            )}

            {/* Tags */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="muted-glassmorphic rounded-xl p-6 glow-border"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {course.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-galaxy-blue/20 text-galaxy-blue px-3 py-1 rounded-full text-sm border border-galaxy-blue/30"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
