'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import toast from 'react-hot-toast';
import {
  Search,
  Filter,
  BookOpen,
  Clock,
  Users,
  Star,
  Play,
  Award
} from 'lucide-react';

interface Course {
  _id: string;
  title: string;
  description: string;
  instructor: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
  thumbnail: string;
  pricing: {
    isFree: boolean;
    price?: number;
  };
  enrollment: {
    totalStudents: number;
  };
  ratings: {
    average: number;
  };
}

const categories = [
  'All Categories',
  'Space Technology',
  'AI in Space',
  'Cybersecurity',
  'Data Science',
  'Robotics',
  'Astrophysics',
];

const levels = ['All Levels', 'beginner', 'intermediate', 'advanced'];

export default function CoursesPage() {
  const { data: session } = useSession();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedLevel, setSelectedLevel] = useState('All Levels');
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState<string | null>(null);

  useEffect(() => {
    fetchCourses();
  }, [searchTerm, selectedCategory, selectedLevel, fetchCourses]);

  const fetchCourses = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory !== 'All Categories') params.append('category', selectedCategory);
      if (selectedLevel !== 'All Levels') params.append('level', selectedLevel);

      const response = await fetch(`/api/courses?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setCourses(data.data.courses);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to load courses');
    } finally {
      setLoading(false);
    }
  }, [searchTerm, selectedCategory, selectedLevel]);

  const handleEnroll = async (courseId: string) => {
    if (!session) {
      toast.error('Please sign in to enroll in courses');
      return;
    }

    try {
      setEnrolling(courseId);
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ courseId }),
      });

      if (response.ok) {
        toast.success('Successfully enrolled in course!');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to enroll in course');
      }
    } catch (error) {
      console.error('Error enrolling in course:', error);
      toast.error('Failed to enroll in course');
    } finally {
      setEnrolling(null);
    }
  };

  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      {/* Hero Section */}
      <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
          <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        </div>

        <div className="relative section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <BookOpen className="h-16 w-16 galaxy-text-blue" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
              Learning Hub
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Master space technology with expert-led courses, hands-on projects, and industry-recognized certifications.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-gray-300">
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <BookOpen className="h-5 w-5 galaxy-text-blue" />
                <span>200+ Courses</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Users className="h-5 w-5 galaxy-text-purple" />
                <span>10,000+ Students</span>
              </div>
              <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                <Award className="h-5 w-5 galaxy-text-pink" />
                <span>Industry Certified</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="relative py-8 z-10">
        <div className="section-container">
          <div className="muted-glassmorphic rounded-2xl p-6 glow-border">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 galaxy-text-blue h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search courses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 glow-border"
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 galaxy-text-purple" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white glow-border"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Level Filter */}
              <div>
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="px-4 py-3 muted-glassmorphic rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white glow-border"
                >
                  {levels.map(level => (
                    <option key={level} value={level}>
                      {level === 'All Levels' ? level : level.charAt(0).toUpperCase() + level.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Grid */}
      <section className="relative py-12 z-10">
        <div className="section-container">
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="bento-grid">
              {courses.map((course, index) => (
                <motion.div
                  key={course._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bento-item muted-glassmorphic glow-border hover:bg-white/5 transition-all duration-300 overflow-hidden group"
                >
                  {/* Course Thumbnail */}
                  <div className="relative h-48 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg mb-4">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Play className="h-12 w-12 text-white opacity-80 group-hover:scale-110 transition-transform" />
                    </div>
                    {course.pricing.isFree && (
                      <div className="absolute top-3 left-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                        Free
                      </div>
                    )}
                  </div>

                  {/* Course Content */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium galaxy-text-blue bg-blue-600/20 px-2 py-1 rounded-full">
                        {course.category}
                      </span>
                      <span className="text-xs text-gray-400 capitalize">
                        {course.level}
                      </span>
                    </div>

                    <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2 font-orbitron">
                      {course.title}
                    </h3>

                    <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                      {course.description}
                    </p>

                    <div className="text-sm text-gray-400 mb-4">
                      By {course.instructor}
                    </div>

                    {/* Course Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 galaxy-text-blue" />
                        <span>{course.duration}h</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4 galaxy-text-purple" />
                        <span>{course.enrollment.totalStudents.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 galaxy-text-pink fill-current" />
                        <span>{course.ratings.average.toFixed(1)}</span>
                      </div>
                    </div>

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between">
                      <div className="text-lg font-bold text-white font-orbitron">
                        {course.pricing.isFree ? 'Free' : `$${course.pricing.price}`}
                      </div>
                      <button
                        onClick={() => handleEnroll(course._id)}
                        disabled={enrolling === course._id}
                        className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 text-sm font-medium disabled:opacity-50 glow-border"
                      >
                        {enrolling === course._id ? 'Enrolling...' : 'Enroll Now'}
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {!loading && courses.length === 0 && (
            <div className="text-center py-12">
              <div className="muted-glassmorphic rounded-2xl p-8 glow-border">
                <BookOpen className="h-16 w-16 galaxy-text-blue mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2 glow-text font-orbitron">No courses found</h3>
                <p className="text-gray-300">Try adjusting your search criteria or browse all courses.</p>
              </div>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}
