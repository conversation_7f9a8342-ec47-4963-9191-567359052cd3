'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';

import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { StudentLayout } from '@/components/layout/StudentLayout';
import { OrganisationLayout } from '@/components/layout/OrganisationLayout';
import { AdminLayout } from '@/components/layout/AdminLayout';
import {
  MessageSquare,
  Users,
  Heart,
  Reply,
  Search,
  Filter,
  Plus,
  ArrowRight,
  Star,
  Clock,
  Eye
} from 'lucide-react';

// Mock data for forum posts
const mockPosts = [
  {
    id: 1,
    title: 'Best practices for satellite attitude control systems?',
    content: 'I\'m working on a CubeSat project and need advice on attitude control. What are the most reliable and cost-effective solutions?',
    author: '<PERSON>',
    authorRole: 'Engineering Student',
    avatar: '/api/placeholder/40/40',
    category: 'Satellite Engineering',
    replies: 23,
    likes: 45,
    views: 234,
    timeAgo: '2 hours ago',
    isPinned: false,
    tags: ['CubeSat', 'Attitude Control', 'Engineering'],
  },
  {
    id: 2,
    title: 'AI algorithms for satellite imagery analysis - Open source tools?',
    content: 'Looking for recommendations on open-source AI tools for analyzing satellite imagery. Particularly interested in land use classification.',
    author: 'Dr. Kwame Asante',
    authorRole: 'AI Researcher',
    avatar: '/api/placeholder/40/40',
    category: 'AI in Space',
    replies: 18,
    likes: 67,
    views: 456,
    timeAgo: '5 hours ago',
    isPinned: true,
    tags: ['AI', 'Satellite Imagery', 'Machine Learning'],
  },
  {
    id: 3,
    title: 'Career transition from software to space tech - Advice needed',
    content: 'I\'m a software engineer looking to transition into space technology. What skills should I focus on and what are the best entry points?',
    author: 'Ahmed Hassan',
    authorRole: 'Software Engineer',
    avatar: '/api/placeholder/40/40',
    category: 'Career Development',
    replies: 31,
    likes: 89,
    views: 678,
    timeAgo: '1 day ago',
    isPinned: false,
    tags: ['Career', 'Software', 'Transition'],
  },
  {
    id: 4,
    title: 'Cybersecurity challenges in satellite communications',
    content: 'With increasing cyber threats, what are the main security considerations for satellite communication systems?',
    author: 'Dr. Fatima Al-Rashid',
    authorRole: 'Cybersecurity Expert',
    avatar: '/api/placeholder/40/40',
    category: 'Cybersecurity',
    replies: 15,
    likes: 52,
    views: 289,
    timeAgo: '2 days ago',
    isPinned: false,
    tags: ['Cybersecurity', 'Satellites', 'Communications'],
  },
];

const categories = [
  'All Categories',
  'Satellite Engineering',
  'AI in Space',
  'Cybersecurity',
  'Career Development',
  'Data Science',
  'Robotics',
  'General Discussion'
];

export default function CommunityPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [sortBy, setSortBy] = useState('recent');

  const searchParams = useSearchParams();
  const dashboardContext = searchParams.get('from');

  // Determine if this page is being accessed from a dashboard
  const isDashboardContext = dashboardContext && ['student', 'organisation', 'admin', 'mentor'].includes(dashboardContext);

  // Community content component
  const CommunityContent = () => {
    if (isDashboardContext) {
      // Dashboard context - no header/footer, just content
      return (
        <>
          {/* Hero Section */}
          <section className="relative pt-8 pb-20 overflow-hidden parallax-container z-10">
            {/* Cosmic Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
              <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
            </div>

            <div className="relative section-container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center"
              >
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <Users className="h-16 w-16 galaxy-text-blue" />
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
                  </div>
                </div>
                <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
                  Community Forum
                </h1>
                <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                  Connect with fellow space technology enthusiasts, share knowledge,
                  ask questions, and collaborate on projects.
                </p>
                <div className="flex flex-wrap justify-center gap-6 text-gray-300">
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <Users className="h-5 w-5 galaxy-text-blue" />
                    <span>10,000+ Members</span>
                  </div>
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <MessageSquare className="h-5 w-5 galaxy-text-purple" />
                    <span>5,000+ Discussions</span>
                  </div>
                  <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                    <Star className="h-5 w-5 galaxy-text-pink" />
                    <span>Expert Contributors</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>
          {renderCommunityContent()}
        </>
      );
    }

    // Standalone page - with header/footer
    return (
      <div className="min-h-screen galaxy-background">
        {/* Star Field Background */}
        <div className="star-field"></div>

        {/* Muted Galaxy Background Overlay */}
        <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

        <Header />

        {/* Hero Section */}
        <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
          {/* Cosmic Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
            <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
          </div>

          <div className="relative section-container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <Users className="h-16 w-16 galaxy-text-blue" />
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
                Community Forum
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                Connect with fellow space technology enthusiasts, share knowledge,
                ask questions, and collaborate on projects.
              </p>
              <div className="flex flex-wrap justify-center gap-6 text-gray-300">
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <Users className="h-5 w-5 galaxy-text-blue" />
                  <span>10,000+ Members</span>
                </div>
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <MessageSquare className="h-5 w-5 galaxy-text-purple" />
                  <span>5,000+ Discussions</span>
                </div>
                <div className="flex items-center space-x-2 muted-glassmorphic px-4 py-2 rounded-full glow-border">
                  <Star className="h-5 w-5 galaxy-text-pink" />
                  <span>Expert Contributors</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {renderCommunityContent()}

        <Footer />
      </div>
    );
  };

  // Shared community content (stats, filters, posts, etc.)
  const renderCommunityContent = () => (
    <>

      {/* Community Stats */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-indigo-600">10,234</div>
              <div className="text-gray-600">Active Members</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-indigo-600">5,678</div>
              <div className="text-gray-600">Discussions</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-indigo-600">23,456</div>
              <div className="text-gray-600">Replies</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-indigo-600">150+</div>
              <div className="text-gray-600">Experts</div>
            </div>
          </div>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-6 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-center flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search discussions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                <option value="recent">Most Recent</option>
                <option value="popular">Most Popular</option>
                <option value="replies">Most Replies</option>
                <option value="views">Most Views</option>
              </select>
            </div>

            {/* New Post Button */}
            <button className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>New Discussion</span>
            </button>
          </div>
        </div>
      </section>

      {/* Forum Posts */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-4">
            {mockPosts.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${
                  post.isPinned ? 'border-l-4 border-indigo-500' : ''
                }`}
              >
                <div className="flex items-start space-x-4">
                  {/* Author Avatar */}
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {post.author.split(' ').map(n => n[0]).join('')}
                  </div>

                  {/* Post Content */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-xs font-medium text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full">
                        {post.category}
                      </span>
                      {post.isPinned && (
                        <span className="text-xs font-medium text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full">
                          Pinned
                        </span>
                      )}
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-indigo-600 cursor-pointer">
                      {post.title}
                    </h3>

                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {post.content}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      {post.tags.map((tag, idx) => (
                        <span
                          key={idx}
                          className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>

                    {/* Author Info */}
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <span className="font-medium text-gray-900">{post.author}</span>
                      <span>{post.authorRole}</span>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{post.timeAgo}</span>
                      </div>
                    </div>

                    {/* Post Stats */}
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div className="flex items-center space-x-1 hover:text-indigo-600 cursor-pointer">
                        <Reply className="h-4 w-4" />
                        <span>{post.replies} replies</span>
                      </div>
                      <div className="flex items-center space-x-1 hover:text-red-600 cursor-pointer">
                        <Heart className="h-4 w-4" />
                        <span>{post.likes} likes</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{post.views} views</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-8">
            <button className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-300 transition-colors duration-200">
              Load More Discussions
            </button>
          </div>
        </div>
      </section>

      {/* Community Guidelines */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Community Guidelines
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Help us maintain a respectful and productive environment for everyone.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                title: 'Be Respectful',
                description: 'Treat all community members with respect and courtesy. No harassment, discrimination, or offensive language.',
                icon: Heart,
              },
              {
                title: 'Stay On Topic',
                description: 'Keep discussions relevant to space technology and related fields. Use appropriate categories for your posts.',
                icon: MessageSquare,
              },
              {
                title: 'Share Knowledge',
                description: 'Help others learn by sharing your expertise and experiences. Provide constructive feedback and support.',
                icon: Star,
              },
            ].map((guideline, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <guideline.icon className="h-8 w-8 text-indigo-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {guideline.title}
                </h3>
                <p className="text-gray-600">
                  {guideline.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Community CTA */}
      <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl font-bold mb-4">
              Ready to Join the Conversation?
            </h2>
            <p className="text-xl text-indigo-100 mb-8 max-w-2xl mx-auto">
              Connect with thousands of space technology enthusiasts and experts from across Africa.
            </p>
            <button className="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-2 mx-auto">
              <span>Join Community</span>
              <ArrowRight className="h-5 w-5" />
            </button>
          </motion.div>
        </div>
      </section>
    </>
  );

  // Render with appropriate layout based on context
  if (isDashboardContext) {
    switch (dashboardContext) {
      case 'student':
        return (
          <StudentLayout>
            <CommunityContent />
          </StudentLayout>
        );
      case 'organisation':
        return (
          <OrganisationLayout>
            <CommunityContent />
          </OrganisationLayout>
        );
      case 'admin':
        return (
          <AdminLayout>
            <CommunityContent />
          </AdminLayout>
        );
      default:
        return <CommunityContent />;
    }
  }

  // Default standalone page layout
  return <CommunityContent />;
}
