'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import {
  Rocket,
  Target,
  Users,
  Globe,
  Heart,
  Lightbulb,
  Shield
} from 'lucide-react';

const teamMembers = [
  {
    name: 'Dr. <PERSON><PERSON>',
    role: 'Founder & CEO',
    bio: 'Former NASA engineer with 15+ years in satellite systems. Passionate about developing Africa&apos;s space capabilities.',
    image: '/api/placeholder/200/200',
  },
  {
    name: 'Prof. <PERSON><PERSON><PERSON>',
    role: 'Chief Technology Officer',
    bio: 'AI researcher and space technology expert. Leading our platform&apos;s technical innovation and AI integration.',
    image: '/api/placeholder/200/200',
  },
  {
    name: '<PERSON>',
    role: 'Head of Education',
    bio: 'Educational technology specialist focused on creating engaging learning experiences for space technology.',
    image: '/api/placeholder/200/200',
  },
  {
    name: '<PERSON>',
    role: 'Head of Partnerships',
    bio: 'Building strategic partnerships with space agencies, universities, and industry leaders across Africa.',
    image: '/api/placeholder/200/200',
  },
];

const values = [
  {
    icon: Lightbulb,
    title: 'Innovation',
    description: 'We foster creativity and cutting-edge thinking to advance space technology in Africa.',
  },
  {
    icon: Users,
    title: 'Community',
    description: 'We believe in the power of collaboration and knowledge sharing across the continent.',
  },
  {
    icon: Shield,
    title: 'Excellence',
    description: 'We maintain the highest standards in education, mentorship, and career development.',
  },
  {
    icon: Heart,
    title: 'Impact',
    description: 'We are committed to creating meaningful change in Africa\'s space technology landscape.',
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen galaxy-background">
      {/* Star Field Background */}
      <div className="star-field"></div>

      {/* Muted Galaxy Background Overlay */}
      <div className="fixed inset-0 dark-blur-gradient pointer-events-none z-0"></div>

      <Header />

      {/* Hero Section */}
      <section className="relative pt-36 sm:pt-40 pb-20 overflow-hidden parallax-container z-10">
        {/* Cosmic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-600/15 to-pink-600/15 rounded-full filter blur-3xl animate-cosmic-drift" />
          <div className="absolute top-40 right-10 w-80 h-80 bg-gradient-to-r from-purple-600/15 to-blue-600/15 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-2000" />
        </div>

        <div className="relative section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <Rocket className="h-16 w-16 galaxy-text-blue" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full animate-twinkle" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white glow-text font-orbitron">
              About Nova
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Empowering Africa&apos;s next generation of space technology innovators through
              comprehensive education, mentorship, and career opportunities.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="relative py-16 z-10">
        <div className="section-container">
          <div className="grid md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="muted-glassmorphic rounded-2xl p-8 glow-border"
            >
              <div className="flex items-center mb-4">
                <Target className="h-8 w-8 galaxy-text-blue mr-3" />
                <h2 className="text-3xl font-bold text-white font-orbitron">Our Mission</h2>
              </div>
              <p className="text-lg text-gray-300 leading-relaxed">
                To democratize access to space technology education across Africa,
                creating a thriving ecosystem where learners can acquire cutting-edge skills,
                connect with industry experts, and launch successful careers in the space sector.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="muted-glassmorphic rounded-2xl p-8 glow-border"
            >
              <div className="flex items-center mb-4">
                <Globe className="h-8 w-8 galaxy-text-purple mr-3" />
                <h2 className="text-3xl font-bold text-white font-orbitron">Our Vision</h2>
              </div>
              <p className="text-lg text-gray-300 leading-relaxed">
                To position Africa as a global leader in space technology innovation,
                with a skilled workforce that drives the continent&apos;s space ambitions
                and contributes to solving global challenges through space-based solutions.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="relative py-16 z-10">
        <div className="section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4 glow-text font-orbitron">Our Story</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Born from a vision to bridge the space technology skills gap in Africa
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="muted-glassmorphic rounded-2xl p-8 glow-border"
            >
              <p className="text-lg text-gray-300 leading-relaxed mb-6">
                Nova was founded in 2023 by a team of passionate space technology professionals
                who recognized the immense potential of Africa&apos;s youth in the space sector.
                Having worked with leading space agencies and organizations worldwide, our founders
                witnessed firsthand the lack of accessible, high-quality space technology education
                on the continent.
              </p>
              <p className="text-lg text-gray-300 leading-relaxed mb-6">
                We started with a simple belief: that with the right education, mentorship, and
                opportunities, African talent can compete and lead on the global space technology stage.
                Today, Nova serves thousands of learners across 25 African countries, connecting them
                with world-class education and industry experts.
              </p>
              <p className="text-lg text-gray-300 leading-relaxed">
                Our platform combines cutting-edge technology with human expertise, using AI to
                personalize learning experiences while maintaining the personal touch that comes
                from one-on-one mentorship with industry professionals.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="relative py-16 z-10">
        <div className="section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4 glow-text font-orbitron">Our Values</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              The principles that guide everything we do
            </p>
          </motion.div>

          <div className="bento-grid">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bento-item muted-glassmorphic glow-border text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4 glow-border">
                  <value.icon className="h-8 w-8 galaxy-text-blue" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2 font-orbitron">
                  {value.title}
                </h3>
                <p className="text-gray-300">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="relative py-16 z-10">
        <div className="section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4 glow-text font-orbitron">Meet Our Team</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Passionate professionals dedicated to advancing space technology education in Africa
            </p>
          </motion.div>

          <div className="bento-grid">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bento-item muted-glassmorphic glow-border text-center"
              >
                <div className="w-24 h-24 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl glow-border">
                  {member.name.split(' ').map(n => n[0]).join('')}
                </div>
                <h3 className="text-xl font-semibold text-white mb-1 font-orbitron">
                  {member.name}
                </h3>
                <p className="galaxy-text-blue font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-300 text-sm">
                  {member.bio}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Impact Stats */}
      <section className="relative py-16 z-10">
        <div className="section-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold mb-4 text-white glow-text font-orbitron">Our Impact</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Measuring our success through the success of our community
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {[
              { number: '10,000+', label: 'Learners Trained' },
              { number: '500+', label: 'Certificates Issued' },
              { number: '150+', label: 'Industry Mentors' },
              { number: '25', label: 'African Countries' },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="muted-glassmorphic rounded-2xl p-6 glow-border"
              >
                <div className="text-3xl md:text-4xl font-bold mb-2 galaxy-text-blue font-orbitron">{stat.number}</div>
                <div className="text-gray-300">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Us CTA */}
      <section className="relative py-16 z-10">
        <div className="section-container text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="muted-glassmorphic rounded-3xl p-12 glow-border"
          >
            <h2 className="text-3xl font-bold text-white mb-4 glow-text font-orbitron">
              Join Our Mission
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Whether you&apos;re a learner, mentor, or partner, there&apos;s a place for you in the Nova community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-300 glow-border">
                Start Learning
              </button>
              <button className="muted-glassmorphic text-white px-8 py-3 rounded-lg font-semibold hover:bg-white/5 transition-all duration-300 glow-border">
                Become a Mentor
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
