'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';


interface AdminStats {
  totalUsers: number;
  newUsersToday: number;
  totalCourses: number;
  totalJobs: number;
  totalEvents: number;
  pendingApprovals: number;
  systemHealth: number;
  revenue: number;
}

interface PendingApproval {
  id: string;
  type: 'course' | 'resource' | 'event' | 'mentor' | 'employer' | 'partner';
  title?: string;
  name?: string;
  email?: string;
  instructor?: string;
  author?: string;
  organizer?: string;
  company?: string;
  appliedAt?: string;
  createdAt?: string;
  status: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  actionRequired: boolean;
  category: string;
}



export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    newUsersToday: 0,
    totalCourses: 0,
    totalJobs: 0,
    totalEvents: 0,
    pendingApprovals: 0,
    systemHealth: 100,
    revenue: 0,
  });
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, approvalsRes, alertsRes] = await Promise.all([
        fetch('/api/admin/dashboard'),
        fetch('/api/admin/approvals?limit=5'),
        fetch('/api/admin/alerts?limit=5'),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data.overview);
      }

      if (approvalsRes.ok) {
        const approvalsData = await approvalsRes.json();
        setPendingApprovals(approvalsData.data.approvals || []);
      }

      if (alertsRes.ok) {
        const alertsData = await alertsRes.json();
        setSystemAlerts(alertsData.data.alerts || []);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white galaxy-text-pink">
          Admin Dashboard
        </h1>
        <p className="text-gray-300 mt-2">
          Monitor platform health, manage users, and oversee operations.
        </p>
      </div>

      {/* System Health Alert */}
      {stats.systemHealth < 95 && (
        <div className="mb-6 p-4 bg-galaxy-purple/20 border border-galaxy-purple rounded-lg">
          <div className="flex items-center">
            <div className="text-galaxy-purple mr-3">⚠️</div>
            <div>
              <p className="text-white font-medium">System Health: {stats.systemHealth}%</p>
              <p className="text-gray-300 text-sm">Some services may be experiencing issues.</p>
            </div>
            <Link
              href="/dashboard/admin/system"
              className="ml-auto px-3 py-1 bg-galaxy-purple text-white rounded text-sm hover:bg-galaxy-purple-light transition-colors glow-border"
            >
              Check System
            </Link>
          </div>
        </div>
      )}

      {/* Stats Grid - Bento Box Layout */}
      <div className="bento-grid bento-grid-features gap-6 mb-8">
            <StatCard
              title="Total Users"
              value={stats.totalUsers}
              icon="👥"
              link="/dashboard/admin/users"
              change={stats.newUsersToday}
              changeLabel="new today"
            />
            <StatCard
              title="Total Courses"
              value={stats.totalCourses}
              icon="📚"
              link="/dashboard/admin/courses"
            />
            <StatCard
              title="Active Jobs"
              value={stats.totalJobs}
              icon="💼"
              link="/dashboard/admin/jobs"
            />
            <StatCard
              title="Upcoming Events"
              value={stats.totalEvents}
              icon="🎯"
              link="/dashboard/admin/events"
            />
            <StatCard
              title="Pending Approvals"
              value={stats.pendingApprovals}
              icon="⏳"
              link="/dashboard/admin/approvals"
              highlight={stats.pendingApprovals > 0}
            />
            <StatCard
              title="System Health"
              value={stats.systemHealth}
              icon="🔧"
              suffix="%"
              link="/dashboard/admin/system"
              highlight={stats.systemHealth < 95}
            />
            <StatCard
              title="Revenue (MTD)"
              value={stats.revenue}
              icon="💰"
              prefix="$"
              link="/dashboard/admin/revenue"
            />
            <StatCard
              title="Platform Score"
              value={95}
              icon="⭐"
              suffix="/100"
              link="/dashboard/admin/analytics"
            />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Pending Approvals */}
          <div className="lg:col-span-2">
            <div className="muted-glassmorphic bento-item rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white galaxy-text-blue">Pending Approvals</h2>
                <Link
                  href="/dashboard/admin/approvals"
                  className="text-galaxy-blue-light hover:text-galaxy-purple text-sm font-medium transition-colors"
                >
                  View All
                </Link>
              </div>

              {pendingApprovals.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-300">No pending approvals</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {pendingApprovals.map((approval) => (
                    <ApprovalCard key={approval.id} approval={approval} />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions & System Alerts */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="muted-glassmorphic bento-item rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white galaxy-text-purple mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <QuickActionButton
                  href="/dashboard/admin/users"
                  icon="👥"
                  title="Manage Users"
                  description="View and manage user accounts"
                />
                <QuickActionButton
                  href="/dashboard/admin/role-applications"
                  icon="🔄"
                  title="Role Applications"
                  description="Review role upgrade requests"
                />
                <QuickActionButton
                  href="/dashboard/admin/content"
                  icon="📝"
                  title="Content Moderation"
                  description="Review flagged content"
                />
                <QuickActionButton
                  href="/dashboard/admin/analytics"
                  icon="📊"
                  title="Analytics"
                  description="View detailed platform metrics"
                />
                <QuickActionButton
                  href="/dashboard/admin/settings"
                  icon="⚙️"
                  title="System Settings"
                  description="Configure platform settings"
                />
              </div>
            </div>

            {/* System Alerts */}
            <div className="muted-glassmorphic bento-item rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white galaxy-text-pink">System Alerts</h2>
                <Link
                  href="/dashboard/admin/alerts"
                  className="text-galaxy-blue-light hover:text-galaxy-purple text-sm font-medium transition-colors"
                >
                  View All
                </Link>
              </div>
              {systemAlerts.length === 0 ? (
                <p className="text-gray-300 text-sm">No active alerts</p>
              ) : (
                <div className="space-y-3">
                  {systemAlerts.map((alert) => (
                    <AlertCard key={alert.id} alert={alert} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
    </div>
  );
}

function StatCard({
  title,
  value,
  icon,
  link,
  prefix = '',
  suffix = '',
  change,
  changeLabel,
  highlight = false
}: {
  title: string;
  value: number;
  icon: string;
  link?: string;
  prefix?: string;
  suffix?: string;
  change?: number;
  changeLabel?: string;
  highlight?: boolean;
}) {
  const content = (
    <div className={`muted-glassmorphic bento-item rounded-lg p-6 hover:glow-border transition-all duration-300 ${
      highlight ? 'ring-2 ring-galaxy-purple' : ''
    }`}>
      <div className="flex items-center">
        <div className="text-2xl mr-3">{icon}</div>
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-300">{title}</p>
          <p className="text-2xl font-bold text-white">
            {prefix}{value}{suffix}
          </p>
          {change !== undefined && changeLabel && (
            <p className="text-xs text-gray-400 mt-1">
              +{change} {changeLabel}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  return link ? <Link href={link}>{content}</Link> : content;
}

function ApprovalCard({ approval }: { approval: PendingApproval }) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'course': return '📚';
      case 'resource': return '📄';
      case 'event': return '🎯';
      case 'mentor': return '👨‍🏫';
      case 'employer': return '💼';
      case 'partner': return '🤝';
      default: return '📝';
    }
  };

  const getDisplayTitle = (approval: PendingApproval) => {
    if (approval.title) return approval.title;
    if (approval.name) return `${approval.type.charAt(0).toUpperCase() + approval.type.slice(1)} Application - ${approval.name}`;
    return `${approval.type.charAt(0).toUpperCase() + approval.type.slice(1)} Application`;
  };

  const getSubmittedBy = (approval: PendingApproval) => {
    if (approval.instructor) return approval.instructor;
    if (approval.author) return approval.author;
    if (approval.organizer) return approval.organizer;
    if (approval.name) return approval.name;
    if (approval.email) return approval.email;
    return 'Unknown';
  };

  const getSubmittedDate = (approval: PendingApproval) => {
    const date = approval.appliedAt || approval.createdAt;
    return date ? new Date(date).toLocaleDateString() : 'Unknown';
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-start space-x-3">
          <div className="text-xl">{getTypeIcon(approval.type)}</div>
          <div className="flex-1">
            <h3 className="font-medium text-gray-900">{getDisplayTitle(approval)}</h3>
            <p className="text-sm text-gray-600">by {getSubmittedBy(approval)}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                {approval.status}
              </span>
              <span className="text-xs text-gray-500">
                {getSubmittedDate(approval)}
              </span>
            </div>
          </div>
        </div>
        <Link
          href={`/dashboard/admin/approvals/${approval.id}`}
          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
        >
          Review
        </Link>
      </div>
    </div>
  );
}

function QuickActionButton({
  href,
  icon,
  title,
  description
}: {
  href: string;
  icon: string;
  title: string;
  description: string;
}) {
  return (
    <Link
      href={href}
      className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
    >
      <div className="text-xl mr-3">{icon}</div>
      <div>
        <p className="font-medium text-gray-900">{title}</p>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </Link>
  );
}

function AlertCard({ alert }: { alert: SystemAlert }) {
  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'success': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return '🚨';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      case 'success': return '✅';
      default: return '📝';
    }
  };

  return (
    <div className={`p-3 border rounded-lg ${getAlertColor(alert.type)}`}>
      <div className="flex items-start space-x-2">
        <div className="text-sm">{getAlertIcon(alert.type)}</div>
        <div className="flex-1">
          <p className="text-sm font-medium">{alert.title}</p>
          <p className="text-xs text-gray-600 mt-1">{alert.message}</p>
          <div className="flex items-center justify-between mt-2">
            <p className="text-xs text-gray-500">
              {new Date(alert.createdAt).toLocaleString()}
            </p>
            {alert.actionRequired && (
              <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                Action Required
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
