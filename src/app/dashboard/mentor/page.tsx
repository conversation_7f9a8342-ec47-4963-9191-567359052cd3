'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { RoleGuard } from '@/components/auth/RoleGuard';
import { MentorLayout } from '@/components/layout/MentorLayout';

interface MentorStats {
  totalMentees: number;
  totalSessions: number;
  upcomingSessions: number;
  coursesCreated: number;
  totalEarnings: number;
  averageRating: number;
}

interface UpcomingSession {
  _id: string;
  title: string;
  menteeId: {
    name: string;
    email: string;
  };
  scheduledAt: string;
  duration: number;
  meetingType: string;
}

interface RecentMentee {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
  lastSessionAt: string;
  totalSessions: number;
  progress: string;
}

export default function MentorDashboard() {
  const [stats, setStats] = useState<MentorStats>({
    totalMentees: 0,
    totalSessions: 0,
    upcomingSessions: 0,
    coursesCreated: 0,
    totalEarnings: 0,
    averageRating: 0,
  });
  const [upcomingSessions, setUpcomingSessions] = useState<UpcomingSession[]>([]);
  const [recentMentees, setRecentMentees] = useState<RecentMentee[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, sessionsRes, menteesRes] = await Promise.all([
        fetch('/api/dashboard/mentor/stats'),
        fetch('/api/dashboard/mentor/sessions?status=upcoming&limit=5'),
        fetch('/api/dashboard/mentor/mentees?limit=5'),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data);
      }

      if (sessionsRes.ok) {
        const sessionsData = await sessionsRes.json();
        setUpcomingSessions(sessionsData.data);
      }

      if (menteesRes.ok) {
        const menteesData = await menteesRes.json();
        setRecentMentees(menteesData.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-galaxy-blue-light"></div>
      </div>
    );
  }

  return (
    <RoleGuard allowedRoles={['mentor']}>
      <MentorLayout
        totalMentees={stats.totalMentees}
        upcomingSessions={stats.upcomingSessions}
        coursesCreated={stats.coursesCreated}
      >
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white galaxy-text-purple">
              Mentor Dashboard
            </h1>
            <p className="text-gray-300 mt-2">
              Manage your mentees, sessions, and track your impact.
            </p>
          </div>

          {/* Stats Grid - Bento Box Layout */}
          <div className="bento-grid bento-grid-features mb-8">
            <StatCard
              title="Total Mentees"
              value={stats.totalMentees}
              icon="👥"
              link="/dashboard/mentor/mentees"
            />
            <StatCard
              title="Total Sessions"
              value={stats.totalSessions}
              icon="📅"
              link="/dashboard/mentor/sessions"
            />
            <StatCard
              title="Upcoming Sessions"
              value={stats.upcomingSessions}
              icon="⏰"
              link="/dashboard/mentor/sessions?filter=upcoming"
            />
            <StatCard
              title="Courses Created"
              value={stats.coursesCreated}
              icon="📚"
              link="/dashboard/mentor/courses"
            />
            <StatCard
              title="Total Earnings"
              value={stats.totalEarnings}
              icon="💰"
              prefix="$"
              link="/dashboard/mentor/earnings"
            />
            <StatCard
              title="Average Rating"
              value={stats.averageRating}
              icon="⭐"
              suffix="/5"
              decimal={1}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Upcoming Sessions */}
            <div className="lg:col-span-2">
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-white galaxy-text-blue">Upcoming Sessions</h2>
                  <Link
                    href="/dashboard/mentor/sessions"
                    className="text-galaxy-blue-light hover:text-galaxy-purple text-sm font-medium transition-colors"
                  >
                    View All Sessions
                  </Link>
                </div>

                {upcomingSessions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-300 mb-4">No upcoming sessions scheduled.</p>
                    <Link
                      href="/mentorship/availability"
                      className="inline-flex items-center px-4 py-2 bg-galaxy-blue text-white rounded-md hover:bg-galaxy-blue-light transition-colors glow-border"
                    >
                      Set Availability
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {upcomingSessions.map((session) => (
                      <SessionCard key={session._id} session={session} />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions & Recent Mentees */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <h2 className="text-xl font-semibold text-white galaxy-text-purple mb-4">Quick Actions</h2>
                <div className="space-y-3">
                  <QuickActionButton
                    href="/courses/create"
                    icon="📚"
                    title="Create Course"
                    description="Share your knowledge"
                  />
                  <QuickActionButton
                    href="/mentorship/availability"
                    icon="📅"
                    title="Set Availability"
                    description="Manage your schedule"
                  />
                  <QuickActionButton
                    href="/dashboard/mentor/mentees"
                    icon="👥"
                    title="View Mentees"
                    description="Check mentee progress"
                  />
                  <QuickActionButton
                    href="/events/create"
                    icon="🎯"
                    title="Create Event"
                    description="Host a workshop"
                  />
                </div>
              </div>

              {/* Recent Mentees */}
              <div className="muted-glassmorphic bento-item rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-white galaxy-text-pink">Recent Mentees</h2>
                  <Link
                    href="/dashboard/mentor/mentees"
                    className="text-galaxy-blue-light hover:text-galaxy-purple text-sm font-medium transition-colors"
                  >
                    View All
                  </Link>
                </div>
                {recentMentees.length === 0 ? (
                  <p className="text-gray-300 text-sm">No mentees yet</p>
                ) : (
                  <div className="space-y-3">
                    {recentMentees.map((mentee) => (
                      <MenteeCard key={mentee._id} mentee={mentee} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </MentorLayout>
    </RoleGuard>
  );
}

function StatCard({
  title,
  value,
  icon,
  link,
  prefix = '',
  suffix = '',
  decimal = 0
}: {
  title: string;
  value: number;
  icon: string;
  link?: string;
  prefix?: string;
  suffix?: string;
  decimal?: number;
}) {
  const displayValue = decimal > 0 ? value.toFixed(decimal) : value.toString();

  const content = (
    <div className="muted-glassmorphic bento-item rounded-lg p-6 hover:glow-border transition-all duration-300">
      <div className="flex items-center">
        <div className="text-2xl mr-3">{icon}</div>
        <div>
          <p className="text-sm font-medium text-gray-300">{title}</p>
          <p className="text-2xl font-bold text-white">
            {prefix}{displayValue}{suffix}
          </p>
        </div>
      </div>
    </div>
  );

  return link ? <Link href={link}>{content}</Link> : content;
}

function SessionCard({ session }: { session: UpcomingSession }) {
  const sessionDate = new Date(session.scheduledAt);
  const isSoon = sessionDate.getTime() - new Date().getTime() < 2 * 60 * 60 * 1000; // 2 hours

  return (
    <div className={`p-4 border rounded-lg transition-colors ${isSoon ? 'border-galaxy-purple bg-galaxy-purple/10' : 'border-white/10 hover:bg-white/5'}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-medium text-white">{session.title}</h3>
          <p className="text-sm text-gray-300">with {session.menteeId.name}</p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-400">
            <span>📅 {sessionDate.toLocaleDateString()}</span>
            <span>⏰ {sessionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
            <span>⏱️ {session.duration} min</span>
            <span>🎥 {session.meetingType}</span>
          </div>
        </div>
        <div className="flex space-x-2">
          {isSoon && (
            <span className="px-2 py-1 bg-galaxy-purple text-white text-xs rounded-full">
              Soon
            </span>
          )}
          <Link
            href={`/mentorship/sessions/${session._id}`}
            className="px-3 py-1 bg-galaxy-blue text-white text-sm rounded hover:bg-galaxy-blue-light transition-colors glow-border"
          >
            View
          </Link>
        </div>
      </div>
    </div>
  );
}

function QuickActionButton({
  href,
  icon,
  title,
  description
}: {
  href: string;
  icon: string;
  title: string;
  description: string;
}) {
  return (
    <Link
      href={href}
      className="flex items-center p-3 border border-white/10 rounded-lg hover:bg-white/5 hover:glow-border transition-all duration-300"
    >
      <div className="text-xl mr-3">{icon}</div>
      <div>
        <p className="font-medium text-white">{title}</p>
        <p className="text-sm text-gray-300">{description}</p>
      </div>
    </Link>
  );
}

function MenteeCard({ mentee }: { mentee: RecentMentee }) {
  return (
    <div className="flex items-center space-x-3 p-3 border border-white/10 rounded-lg hover:bg-white/5 transition-colors">
      <div className="flex-shrink-0">
        {mentee.avatar ? (
          <Image
            src={mentee.avatar}
            alt={mentee.name}
            width={40}
            height={40}
            className="w-10 h-10 rounded-full"
          />
        ) : (
          <div className="w-10 h-10 bg-galaxy-deep rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-300">
              {mentee.name.charAt(0).toUpperCase()}
            </span>
          </div>
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-white truncate">{mentee.name}</p>
        <p className="text-xs text-gray-300">{mentee.totalSessions} sessions</p>
        <p className="text-xs text-gray-400">
          Last: {new Date(mentee.lastSessionAt).toLocaleDateString()}
        </p>
      </div>
      <Link
        href={`/dashboard/mentor/mentees/${mentee._id}`}
        className="text-galaxy-blue-light hover:text-galaxy-purple text-sm transition-colors"
      >
        View
      </Link>
    </div>
  );
}
